<template>
	<x-popup ref="popupRef" :hfull="false" @open="handlePopupOpen">
		<!-- 下拉菜单触发器 -->
		<template #default="{ isOpen }">
			<div class="wfull flex flex-wrap cursor-text items-center gap-1 overflow-hidden">
				<!-- 多选已选内容 -->
				<template v-if="multiple && modelValue?.length">
					<span
						v-for="(item, index) in selectedLabels"
						:key="index"
						class="flex items-center rounded-1 bg-primary text-white text-sm px-xxs-1.5 py-xxs-0.2 space-x-1"
					>
						<span>{{ item }}</span>
						<div class="min-w-1rem">
							<XIconsCloseLine v-if="showClear" class="cursor-pointer text-aid hover:text-white" @click.stop="removeSelected(index)" />
						</div>
					</span>
				</template>

				<!-- 搜索框/单选项显示 -->
				<x-input
					ref="searchInput"
					v-model="searchQuery"
					:class="$attrs.class"
					:placeholder="placeholder"
					:disabled="disabled"
					:readonly="!actualSearchable"
					:custom-class="[{ '!placeholder-inputText': X_COMMON_UTILS.isDef(modelValue) }]"
					:clearable="clearable"
					:display-value="selectedLabel"
					:skip-blur-validation="true"
					@keydown.backspace="handleBackspace"
					@input="
						() => {
							localOptions = []
							handleSearch()
						}
					"
					@clear="clearSelected"
				/>
			</div>
		</template>

		<!-- 下拉选项内容 -->
		<template #content>
			<div
				v-loading="isLoading"
				class="max-h-20rem min-w-5rem overflow-y-auto scroll-smooth rounded bg-inputBg color-inputText shadow-lg mt-xxs p-xs-0.8 border-inputBorder"
				:style="{ width: triggerWidth + 'px' }"
			>
				<!-- 选项列表 -->
				<div class="flex flex-col gap-xxs">
					<template v-if="filteredOptions.length">
						<div
							v-for="(option, index) in filteredOptions"
							:key="index"
							class="cursor-pointer rounded-md p-xxs-sm"
							:class="{
								'bg-primary/30': isSelected(option.value),
								'hover:bg-primaryLight': !isSelected(option.value),
								'text-gray-400': option.disabled,
								'cursor-not-allowed': option.disabled,
							}"
							@click="selectOption(option)"
						>
							<span v-html="highlightMatch(option.label, searchQuery)"></span>
						</div>
					</template>
					<div v-else class="p-2 text-gray-400">
						{{ searchQuery ? '无匹配结果' : '无数据' }}
					</div>
				</div>
			</div>
		</template>
	</x-popup>
</template>

<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'

// ============== 组件Props定义 ==============
const props = defineProps({
	options: { type: Array, default: () => [] },
	multiple: { type: Boolean, default: false },
	placeholder: { type: String, default: '请选择' },
	searchable: { type: Boolean, default: false },
	clearable: { type: Boolean, default: true },
	remoteMethod: { type: Function, default: null },
	immediateSearch: { type: Boolean, default: true },
	labelKey: { type: String, default: 'name' },
	valueKey: { type: String, default: 'id' },
	tagGroupName: { type: String },
	tagValueFormat: { type: String, default: 'default' },
	getItemById: { type: Function, default: null },
	disabled: { type: Boolean, default: false },
})

// ============== 组件Events定义 ==============
const emit = defineEmits(['search', 'clear', 'change', 'update:label'])

// ============== 组件引用 ==============
const popupRef = ref()
const searchInput = ref(null)
const triggerWidth = ref(200) // 默认宽度

// ============== 响应式数据 ==============
const modelValue = defineModel({ default: null })
const searchQuery = ref('')
const localOptions = ref([])
const isLoading = ref(false)

// ============== 表单上下文处理 ==============
const formContext = inject<any>('formContext', null)
const { prop: formItemProp, error } = inject<string | undefined>('formItemContext', {})

function formValidate(trigger) {
	formContext?.validateField?.(formItemProp!, trigger)
}

// ============== 标签数据处理 ==============
const {
	run: getTagListRun,
	loading: getTagListLoading,
	data: tagList,
} = xUseRequest(BIZ_OPEN_APIS.getTagListStore, `${props.tagGroupName}&format=${props.tagValueFormat}`)

// ============== 计算属性 ==============
// 实际的搜索状态（有远程方法时默认为true）
const actualSearchable = computed(() => {
	return props.remoteMethod ? true : props.searchable
})

// 是否显示清除按钮
const showClear = computed(() => {
	return (
		props.clearable &&
		(props.multiple ? modelValue.value?.length > 0 : modelValue.value !== null && modelValue.value !== undefined && modelValue.value !== '')
	)
})

// 过滤后的选项
const filteredOptions = computed(() => {
	if (props.remoteMethod) return localOptions.value
	return localOptions.value.filter((option) => fuzzyMatch(option.label, searchQuery.value))
})

// 多选时已选项的标签
const selectedLabels = computed(() => {
	if (!props.multiple || !modelValue.value) return []
	return modelValue.value
		.map((value) => {
			const option = localOptions.value.find((option) => option.value === value)
			return option ? option.label : null
		})
		.filter((label) => label !== null)
})

// 单选时已选项的标签
const selectedLabel = computed(() => {
	if (props.multiple) return ''
	const option = localOptions.value.find((opt) => opt?.value === modelValue.value)
	return option?.label || ''
})

// ============== 生命周期 ==============
onMounted(async () => {
	await initializeOptions()
	await loadSelectedItemsIfNeeded()
})

// ============== 监听器 ==============
// 监听options变化
watchEffect(() => {
	if (!props.tagGroupName) {
		localOptions.value = formatOptions(props.options)
	}
})

// 监听modelValue变化，加载选中项详情
watch(
	modelValue,
	async (newVal) => {
		if (props.getItemById && newVal) {
			await loadSelectedItemsIfNeeded()
		}
	},
	{ deep: true },
)

// 处理popup打开事件，在打开时发起立即请求
const handlePopupOpen = async () => {
	if (props.remoteMethod && props.immediateSearch && localOptions.value.length === 0) {
		await loadRemoteData()
	}
}

// ============== 核心方法 ==============
// 初始化选项数据
async function initializeOptions() {
	if (props.tagGroupName) {
		isLoading.value = true
		try {
			await getTagListRun()
			localOptions.value = formatOptions(tagList.value?.tagList || [])
		} finally {
			isLoading.value = false
		}
	} else if (!props.remoteMethod) {
		// 只有在没有远程方法时才使用本地options
		localOptions.value = formatOptions(props.options)
	}
}

// 格式化选项数据
function formatOptions(list) {
	if (!Array.isArray(list)) return []
	return list.map((item) => ({
		...item,
		label: item[props.labelKey],
		value: item[props.valueKey],
	}))
}

// 加载选中项的详细信息
async function loadSelectedItemsIfNeeded() {
	if (!props.getItemById || !modelValue.value) return

	const idsToLoad = getIdsToLoad()
	if (!idsToLoad.length) return

	isLoading.value = true
	try {
		const loadPromises = idsToLoad.map((id) => props.getItemById(id).catch(() => null))
		const results = await Promise.all(loadPromises)
		const validResults = results.filter((item) => item !== null)

		if (validResults.length) {
			const formattedResults = formatOptions(validResults)
			addUniqueOptions(formattedResults)
		}
	} finally {
		isLoading.value = false
	}
}

// 获取需要加载的ID列表
function getIdsToLoad() {
	if (props.multiple) {
		return modelValue.value?.filter((id) => !localOptions.value.some((opt) => opt.value === id)) || []
	} else {
		return modelValue.value && !localOptions.value.some((opt) => opt.value === modelValue.value) ? [modelValue.value] : []
	}
}

// 添加唯一选项到本地选项中
function addUniqueOptions(newOptions) {
	newOptions.forEach((item) => {
		if (!localOptions.value.some((opt) => opt.value === item.value)) {
			localOptions.value.push(item)
		}
	})
}

// ============== 搜索相关方法 ==============
// 防抖搜索处理
const handleSearch = useDebounceFn(async () => {
	if (props.remoteMethod) {
		await loadRemoteData()
	} else {
		emit('search', searchQuery.value)
	}
}, 700)

// 远程数据加载
async function loadRemoteData() {
	if (!props.remoteMethod) return

	isLoading.value = true
	try {
		const res = await props.remoteMethod({
			[props.labelKey]: searchQuery.value,
			attribute: 'ANY',
		})

		const list = Array.isArray(res) ? res : res?.list || res?.data || []
		localOptions.value = formatOptions(list)
	} catch (error) {
		console.error('远程数据加载失败:', error)
		localOptions.value = []
	} finally {
		isLoading.value = false
	}
}

// ============== 选项操作方法 ==============
// 选择选项
function selectOption(option) {
	if (option.disabled) return

	const newValue = props.multiple ? toggleMultipleSelection(option.value) : selectSingleOption(option.value)

	modelValue.value = newValue
	emit('change', newValue, option)
	emit('update:label', option.label)
	formValidate('change')
}

// 切换多选项
function toggleMultipleSelection(value) {
	const currentValues = modelValue.value || []
	return currentValues.includes(value) ? currentValues.filter((item) => item !== value) : [...currentValues, value]
}

// 选择单个选项
function selectSingleOption(value) {
	searchQuery.value = ''
	closePopup()
	return value
}

// 清除选择
function clearSelected() {
	modelValue.value = undefined
	inputFocus()
	formValidate('change')
	emit('clear')
	emit('change', undefined)
	emit('update:label', undefined)
}

// 判断是否已选中
function isSelected(value) {
	return props.multiple ? modelValue.value?.includes(value) : modelValue.value === value
}

// ============== 键盘事件处理 ==============
// 处理退格键删除
function handleBackspace(e) {
	if (props.multiple && modelValue.value?.length > 0 && searchQuery.value === '') {
		const lastIndex = modelValue.value.length - 1
		removeSelected(lastIndex)
	}
}

// 移除选中项
function removeSelected(index) {
	if (modelValue.value && modelValue.value.length > index) {
		modelValue.value.splice(index, 1)
		inputFocus()
	}
}

// ============== 辅助方法 ==============
// 关闭弹窗
function closePopup() {
	popupRef.value?.close()
}

// 输入框获取焦点
function inputFocus() {
	if (actualSearchable.value) {
		nextTick(() => {
			searchInput.value?.focus()
		})
	}
}

// 模糊匹配
function fuzzyMatch(text, query) {
	if (!query) return true
	const queryChars = query.toLowerCase().split('')
	const textLower = text.toLowerCase()
	let textIndex = 0

	for (const char of queryChars) {
		const index = textLower.indexOf(char, textIndex)
		if (index === -1) return false
		textIndex = index + 1
	}
	return true
}

// 高亮匹配文本
function highlightMatch(text, query) {
	if (!query || !text) return text

	const regex = new RegExp(`(${query.split('').join('|')})`, 'gi')
	return text.replace(regex, '<span class="text-blue-600 font-semibold">$1</span>')
}
</script>
