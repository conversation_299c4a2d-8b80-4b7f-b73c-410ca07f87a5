<template>
	<transition appear :css="false" @before-enter="beforeEnter" @enter="enter" @leave="leave">
		<slot></slot>
	</transition>
</template>

<script setup lang="ts">
import gsap from 'gsap'

// 定义可能被传递的事件
const emit = defineEmits(['after-enter', 'after-leave', 'before-enter', 'before-leave', 'enter', 'leave', 'click'])

const props = defineProps({
	mode: {
		type: String as PropType<'fade' | 'slide' | 'scale' | 'flip' | 'elastic' | 'rebound' | 'bounce'>,
		default: 'fade',
	},
	duration: {
		type: Number,
		default: 0.3,
	},
	direction: {
		type: String as PropType<'left' | 'right' | 'up' | 'down' | 'center' | 'none'>,
		default: 'center',
	},
	ease: {
		type: String,
		default: 'power4.inOut',
	},
	immediate: {
		// 新增 immediate 属性
		type: Boolean,
		default: false,
	},
})

const transformMap = {
	fade: {
		left: 'translateX(-20px)',
		right: 'translateX(20px)',
		up: 'translateY(-20px)',
		down: 'translateY(20px)',
		center: 'scale(0.95)',
		none: '',
	},
	slide: {
		left: 'translateX(-100%)',
		right: 'translateX(100%)',
		up: 'translateY(-100%)',
		down: 'translateY(100%)',
		center: 'scale(0)', // center方向，元素从中间缩小或放大
		none: '',
	},
	scale: 'scale(0)',
	flip: 'rotateY(90deg)', // flip效果
	// Elastic效果
	elastic: (direction: string) => {
		switch (direction) {
			case 'up':
				return 'scale(0.5) translateY(-50%)' // 上方向
			case 'down':
				return 'scale(0.5) translateY(50%)' // 下方向
			case 'left':
				return 'scale(0.5) translateX(-50%)' // 左方向
			case 'right':
				return 'scale(0.5) translateX(50%)' // 右方向
			case 'center':
				return 'scale(0.5)' // 中心方向
			case 'none':
				return '' // 无变换
			default:
				return 'scale(0.5) translateY(50%)' // 默认下方向
		}
	},
	// 回弹效果
	rebound: (direction: string) => {
		switch (direction) {
			case 'up':
				return 'scale(0.8) translateY(-50%)'
			case 'down':
				return 'scale(0.8) translateY(50%)'
			case 'left':
				return 'scale(0.8) translateX(-50%)'
			case 'right':
				return 'scale(0.8) translateX(50%)'
			case 'center':
				return 'scale(0.8)' // 中心方向
			case 'none':
				return '' // 无变换
			default:
				return 'scale(0.8) translateY(50%)' // 默认下方向
		}
	},
	// 更自然的回弹效果
	bounce: (direction: string) => {
		switch (direction) {
			case 'up':
				return 'scale(0.9) translateY(-30%)'
			case 'down':
				return 'scale(0.9) translateY(30%)'
			case 'left':
				return 'scale(0.9) translateX(-30%)'
			case 'right':
				return 'scale(0.9) translateX(30%)'
			case 'center':
				return 'scale(0.9)' // 中心方向
			case 'none':
				return '' // 无变换
			default:
				return 'scale(0.9) translateY(30%)' // 默认下方向
		}
	},
}

const getInitialTransform = () => {
	const { mode, direction } = props
	// 如果方向是 none，则不应用任何变换
	if (direction === 'none') {
		return ''
	}

	if (mode === 'fade') {
		return transformMap.fade[direction] || 'scale(0.95)'
	}
	if (mode === 'slide') {
		return transformMap.slide[direction] || 'translateX(0)'
	}
	if (mode === 'scale') {
		return transformMap.scale
	}
	if (mode === 'flip') {
		return transformMap.flip
	}
	if (mode === 'elastic') {
		return transformMap.elastic(direction) // 统一使用direction作为方向
	}
	if (mode === 'rebound') {
		return transformMap.rebound(direction) // 回弹效果
	}
	if (mode === 'bounce') {
		return transformMap.bounce(direction) // 更自然的回弹效果
	}
	return 'translateX(0) translateY(0) scale(1)'
}

const getFinalTransform = () => {
	return 'translateX(0) translateY(0) scale(1) rotateY(0)' // 结束时恢复
}

const beforeEnter = (el: HTMLElement) => {
	el.style.opacity = '0'
	el.style.transform = getInitialTransform()
	el.style.willChange = 'transform, opacity' // 提前优化
	emit('before-enter', el)
}

const enter = (el: HTMLElement, done: () => void) => {
	const { mode, duration, ease } = props
	const initialTransform = getInitialTransform()
	const finalTransform = getFinalTransform()

	// 使用GSAP来实现动画
	gsap.set(el, { opacity: 0, transform: initialTransform }) // 使用 gsap.set 避免触发重排

	gsap.fromTo(
		el,
		{ opacity: 0, transform: initialTransform },
		{
			opacity: 1,
			transform: finalTransform,
			duration: mode === 'elastic' ? duration * 1.3 : mode === 'rebound' ? duration * 1.1 : mode === 'bounce' ? duration * 1.3 : duration, // 优化rebound动画时间
			ease: mode === 'elastic' ? 'elastic.out(1, 0.75)' : mode === 'rebound' ? 'back.out(1.2)' : mode === 'bounce' ? 'bounce.out' : ease, // 优化rebound缓动
			onComplete: () => {
				done()
				el.style.transform = '' // 清除 transform
				emit('after-enter', el)
			},
			clearProps: 'transform, opacity',
		},
	)
}

const leave = (el: HTMLElement, done: () => void) => {
	// 新增立即消失逻辑
	if (props.immediate) {
		gsap.set(el, {
			opacity: 0,
			transform: getInitialTransform(),
			onComplete: () => {
				done()
				el.style.transform = ''
				el.style.opacity = ''
			},
			immediateRender: true, // 确保立即生效
		})
		return
	}
	const { mode, duration, ease } = props
	const initialTransform = getInitialTransform() // 使用与进入时相同的初始变换作为离开的目标状态

	// 提前设置 will-change 来优化性能
	el.style.willChange = 'transform, opacity'

	// 动画过渡
	gsap.to(el, {
		opacity: 0,
		transform: initialTransform, // 离开时应该变为初始状态，而不是恢复正常状态
		duration: mode === 'fade' ? duration * 0.75 : duration, // 根据模式调整持续时间
		ease: mode === 'elastic' ? 'elastic.in(1, 0.75)' : mode === 'rebound' ? 'back.in(1.2)' : mode === 'bounce' ? 'bounce.in' : ease, // 优化rebound缓动
		onComplete: () => {
			done()
			el.style.transform = '' // 清除 transform
			emit('after-leave', el)
		},
		clearProps: 'transform, opacity', // 清除动画影响
	})
}
</script>
